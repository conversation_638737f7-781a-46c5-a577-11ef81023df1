const fs = require('fs');

// Read and parse the JSON file
function analyzeMPNDuplicates(filePath) {
    try {
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        // Create a map to store MPN occurrences and associated products
        const mpnMap = new Map();
        
        // Group products by MPN
        data.forEach(product => {
            const mpn = product.mpn;
            if (!mpnMap.has(mpn)) {
                mpnMap.set(mpn, []);
            }
            mpnMap.get(mpn).push(product);
        });

        // Find non-unique MPNs (those with more than one product)
        const nonUniqueMPNs = Array.from(mpnMap.entries())
            .filter(([mpn, products]) => products.length > 1);

        // Count total products with non-unique MPNs
        const totalProductsWithNonUniqueMPNs = nonUniqueMPNs.reduce((sum, [mpn, products]) => 
            sum + products.length, 0);

        // Analyze brand and maincat similarities
        let sameBrandCount = 0;
        let differentBrandCount = 0;
        let sameMaincatCount = 0;
        let differentMaincatCount = 0;
        let bothSameCount = 0;
        let bothDifferentCount = 0;

        nonUniqueMPNs.forEach(([mpn, products]) => {
            const firstProduct = products[0];
            const firstBrand = firstProduct.brand;
            const firstMaincat = firstProduct.maincat;

            products.forEach((product, index) => {
                if (index === 0) return; // Skip first product as it's the reference

                const sameBrand = product.brand === firstBrand;
                const sameMaincat = product.maincat === firstMaincat;

                if (sameBrand) sameBrandCount++;
                else differentBrandCount++;

                if (sameMaincat) sameMaincatCount++;
                else differentMaincatCount++;

                if (sameBrand && sameMaincat) bothSameCount++;
                else if (!sameBrand && !sameMaincat) bothDifferentCount++;
            });
        });

        // Print results
        console.log(`Number of non-unique MPN values: ${nonUniqueMPNs.length}`);
        console.log(`Number of products with non-unique MPNs: ${totalProductsWithNonUniqueMPNs}`);
        console.log(`Products with same brand: ${sameBrandCount}`);
        console.log(`Products with different brand: ${differentBrandCount}`);
        console.log(`Products with same maincat: ${sameMaincatCount}`);
        console.log(`Products with different maincat: ${differentMaincatCount}`);
        console.log(`Products with both same brand and maincat: ${bothSameCount}`);
        console.log(`Products with both different brand and maincat: ${bothDifferentCount}`);

        // Optional: List the non-unique MPNs and their product counts
        // console.log('\nNon-unique MPNs and their product counts:');
        // nonUniqueMPNs.forEach(([mpn, products]) => {
        //     console.log(`MPN: ${mpn}, Product count: ${products.length}`);
        // });

    } catch (error) {
        console.error('Error processing file:', error.message);
    }
}

// Run the analysis
analyzeMPNDuplicates('benco.json');