##frontier might have missed some products
## optimus has mpn but if it is optimus product then it has sku only which is used as mpn in other sellers. i didnot scrape brand nor sku
## make sure all products were scraped from each seller by counting and comparing
## see if there are duplicate mpn for different products then we need to use brand
# henry sometimes have spaces and anumber at the end like "R3001-75 300"
# no core buildup in frontier

Unique (Brand, MPN) combinations that had different URLs: 441
Deduplicated data saved to benco.json

Unique (Brand, MPN) combinations that had different URLs: 3
Deduplicated data saved to dds.json
# no more multiple or null mpn

Unique (Brand, MPN) combinations that had different URLs: 1132
Deduplicated data saved to henry.json

Unique (Brand, MPN) combinations that had different URLs: 384
Deduplicated data saved to optimus.json

Unique (Brand, MPN) combinations that had different URLs: 130
Deduplicated data saved to frontier.json

* if there are multiple products with same mpn we should check first for in stock and out stock then consider them.
----------
--- Analysis for optimus.json ---
Total objects: 21532
Objects with empty 'mpn': 0
Objects with empty 'brand': 0
Objects with empty 'name': 0


--- Analysis for dds.json ---
Total objects: 17316
Objects with empty 'mpn': 0
Objects with empty 'brand': 0
Objects with empty 'name': 0

--- Analysis for henry.json ---
Total objects: 88977
Objects with empty 'mpn': 0
Objects with empty 'brand': 0
Objects with empty 'name': 0
----------------------------

--- Analysis for frontier.json ---
Total objects: 13425
Objects with empty 'mpn': 4150
Objects with empty 'brand': 0
Objects with empty 'name': 0

--- Analysis for benco.json ---
Total objects: 83971
Objects with empty 'mpn': 1
Objects with empty 'brand':0
Objects with empty 'name':0


1. Objects from each seller found in other seller files:
  henry: 37964 objects have MPNs found in other seller files.
  benco: 36517 objects have MPNs found in other seller files.
  frontier: 6545 objects have MPNs found in other seller files.
  dds: 8484 objects have MPNs found in other seller files.
  optimus: 13017 objects have MPNs found in other seller files.

2. Number of unique MPNs existing in all 5 files: 140
3. Number of unique MPNs existing in exactly 4 files: 2519
4. Number of unique MPNs existing in exactly 3 files: 9993

--- Unified Data Statistics ---
Total number of unique MPN entries (products) in unified.json: 157538
Number of objects with data from only 1 seller: 115836
Number of objects with data from 2 sellers: 29050
Number of objects with data from 3 sellers: 9993
Number of objects with data from 4 sellers: 2519
Number of objects with data from 5 sellers: 140

Total number of MPN entries/objects in unified.json that have multiple MPN values from the same seller: 4195