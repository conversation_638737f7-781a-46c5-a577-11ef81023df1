// unifiedPriceAnalyzer.js

const fs = require('fs');
const path = require('path');

const unifiedFilePath = 'unified.json';
const outputFileName = 'unified_updated.json';

/**
 * Parses a price string to extract the numeric value.
 * PRIORITIZES: Value after the first '$' symbol.
 * FALLBACK: If no '$' symbol, attempts to parse the entire string as a number.
 * Correctly handles commas within the numeric part.
 * @param {string} priceString - The price string to parse.
 * @returns {number} The extracted numeric price, or NaN if not found.
 */
function parsePrice(priceString) {
    if (typeof priceString !== 'string') {
        return NaN;
    }

    let extractedNumericString;

    // First, try to find a price after a '$' symbol
    // This regex specifically looks for '$' followed by digits (with optional commas and decimals)
    const dollarPriceMatch = priceString.match(/\$([\d,]+\.?\d*)/);

    if (dollarPriceMatch && dollarPriceMatch[1]) {
        // If a price after '$' is found, use that captured group
        extractedNumericString = dollarPriceMatch[1];
    } else {
        // If no '$' price is found, assume the entire string (after trimming) is the numeric value
        // This handles cases like "53.17"
        extractedNumericString = priceString.trim();
    }

    // Remove all commas from the extracted numeric string before parsing to float
    const cleanedNumericString = extractedNumericString.replace(/,/g, '');

    const priceValue = parseFloat(cleanedNumericString);

    return !isNaN(priceValue) ? priceValue : NaN;
}


/**
 * Processes unified.json to calculate min/max price difference for products
 * available from multiple sellers. Filters out single-seller products and
 * saves the updated data to unified_updated.json.
 * Logs the count difference between the original and updated files.
 */
function analyzeUnifiedPrices() {
    let unifiedData;

    console.log(`\n--- Starting price analysis of ${unifiedFilePath} ---`);

    try {
        const fileContent = fs.readFileSync(unifiedFilePath, 'utf8');
        unifiedData = JSON.parse(fileContent);
    } catch (error) {
        console.error(`Error reading or parsing ${unifiedFilePath}:`, error.message);
        console.log('Please ensure "unified.json" exists in the same directory and is valid JSON.');
        return;
    }

    if (!Array.isArray(unifiedData)) {
        console.error(`Error: The root of ${unifiedFilePath} must be an array of objects.`);
        return;
    }

    const originalUnifiedCount = unifiedData.length;
    const unifiedUpdatedData = [];

    unifiedData.forEach(obj => {
        const prices = [];
        const isTargetMpn = (obj.mpn === "4421303"); // Flag for the specific MPN to debug

        if (isTargetMpn) {
            console.log(`\n--- DEBUGGING MPN: ${obj.mpn} ---`);
        }

        // Iterate through object properties to find price values
        for (const key in obj) {
            // Check if the key starts with a seller name followed by '_price'
            // This covers patterns like 'henry_price', 'benco_price2', etc.
            if (key.endsWith('_price') || key.match(/_price\d+$/)) {
                const priceString = obj[key];
                const priceValue = parsePrice(priceString); // Use the updated parsePrice function

                if (isTargetMpn) {
                    console.log(`  Processing key: ${key}`);
                    console.log(`    Original price string: "${priceString}"`);
                    console.log(`    Parsed price value: ${priceValue}`);
                }

                // Only consider valid numbers for price comparison
                if (!isNaN(priceValue)) {
                    prices.push(priceValue);
                }
            }
        }

        if (isTargetMpn) {
            console.log(`  All valid prices collected for MPN ${obj.mpn}: [${prices.join(', ')}]`);
        }

        // We only care about objects with multiple valid price values
        // If there's only one price, it means no comparison is possible for this MPN in terms of price variation.
        if (prices.length >= 2) {
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const minMaxDifference = maxPrice - minPrice;

            // Add the minmax property to the object
            obj.minmax = minMaxDifference;
            unifiedUpdatedData.push(obj);

            if (isTargetMpn) {
                console.log(`  Min Price: ${minPrice}`);
                console.log(`  Max Price: ${maxPrice}`);
                console.log(`  Calculated MinMax Difference: ${minMaxDifference}`);
                console.log(`--- END DEBUGGING MPN: ${obj.mpn} ---`);
            }
        } else if (isTargetMpn) {
             console.log(`  MPN ${obj.mpn} skipped: Not enough valid prices (found ${prices.length}) for minmax calculation.`);
             console.log(`--- END DEBUGGING MPN: ${obj.mpn} ---`);
        }
    });

    // Save the updated data to a new JSON file
    try {
        fs.writeFileSync(outputFileName, JSON.stringify(unifiedUpdatedData, null, 2), 'utf8');
        console.log(`\nSuccessfully created ${outputFileName} with ${unifiedUpdatedData.length} objects.`);
    } catch (error) {
        console.error(`Error writing to ${outputFileName}:`, error.message);
        return;
    }

    const removedCount = originalUnifiedCount - unifiedUpdatedData.length;

    console.log(`\n--- Analysis Complete ---`);
    console.log(`Original objects in ${unifiedFilePath}: ${originalUnifiedCount}`);
    console.log(`Objects in ${outputFileName} (after filtering): ${unifiedUpdatedData.length}`);
    console.log(`Number of objects removed (products with only one valid price value): ${removedCount}`);
    console.log('--------------------------------------------\n');

    // Confirm the difference as requested
    // Note: The exact number might change slightly due to more accurate parsing.
    if (removedCount === 115836) {
        console.log('Confirmation: The new file contains exactly 115836 fewer objects than unified.json.');
    } else {
        console.log(`Confirmation: The difference is ${removedCount}. This might vary from previous runs due to improved price parsing.`);
    }
}

// Run the analysis
analyzeUnifiedPrices();
