// Import the 'fs' module for file system operations
const fs = require('fs');

/**
 * Analyzes a JSON file to count objects with empty 'mpn', 'brand', and 'name' values.
 * @param {string} filePath - The path to the JSON file.
 * @returns {object} An object containing the counts of empty values.
 */
function analyzeJsonFile(filePath) {
    let emptyMpnCount = 0;
    let emptyBrandCount = 0;
    let emptyNameCount = 0;
    let totalObjects = 0;

    try {
        // Read the file synchronously
        const fileContent = fs.readFileSync(filePath, 'utf8');
        // Parse the JSON content
        const data = JSON.parse(fileContent);

        // Ensure the data is an array
        if (!Array.isArray(data)) {
            console.warn(`Warning: ${filePath} does not contain a JSON array. Skipping analysis.`);
            return {
                emptyMpn: 0,
                emptyBrand: 0,
                emptyName: 0,
                totalObjects: 0
            };
        }

        totalObjects = data.length;

        // Iterate over each object in the array
        data.forEach(obj => {
            // Check for empty 'mpn' value
            // A value is considered empty if it's null, undefined, or an empty string after trimming whitespace
            if (obj.mpn === null || obj.mpn === undefined || (typeof obj.mpn === 'string' && obj.mpn.trim() === '')) {
                emptyMpnCount++;
            }

            // Check for empty 'brand' value
            if (obj.brand === null || obj.brand === undefined || (typeof obj.brand === 'string' && obj.brand.trim() === '')) {
                emptyBrandCount++;
            }

            // Check for empty 'name' value
            if (obj.name === null || obj.name === undefined || (typeof obj.name === 'string' && obj.name.trim() === '')) {
                emptyNameCount++;
            }
        });

        return {
            emptyMpn: emptyMpnCount,
            emptyBrand: emptyBrandCount,
            emptyName: emptyNameCount,
            totalObjects: totalObjects
        };

    } catch (error) {
        // Handle file reading or JSON parsing errors
        console.error(`Error analyzing ${filePath}: ${error.message}`);
        return {
            emptyMpn: 0,
            emptyBrand: 0,
            emptyName: 0,
            totalObjects: 0,
            error: error.message
        };
    }
}

// Define the file paths
const bencoFilePath = 'benco.json';
const ddsFilePath = 'henry.json';

console.log('Starting JSON file analysis...\n');

// Analyze benco.json
const bencoResults = analyzeJsonFile(bencoFilePath);
console.log(`--- Analysis for ${bencoFilePath} ---`);
if (bencoResults.error) {
    console.log(`Could not analyze ${bencoFilePath} due to an error: ${bencoResults.error}`);
} else {
    console.log(`Total objects: ${bencoResults.totalObjects}`);
    console.log(`Objects with empty 'mpn': ${bencoResults.emptyMpn}`);
    console.log(`Objects with empty 'brand': ${bencoResults.emptyBrand}`);
    console.log(`Objects with empty 'name': ${bencoResults.emptyName}`);
}
console.log('\n');

// Analyze henry.json
const ddsResults = analyzeJsonFile(ddsFilePath);
console.log(`--- Analysis for ${ddsFilePath} ---`);
if (ddsResults.error) {
    console.log(`Could not analyze ${ddsFilePath} due to an error: ${ddsResults.error}`);
} else {
    console.log(`Total objects: ${ddsResults.totalObjects}`);
    console.log(`Objects with empty 'mpn': ${ddsResults.emptyMpn}`);
    console.log(`Objects with empty 'brand': ${ddsResults.emptyBrand}`);
    console.log(`Objects with empty 'name': ${ddsResults.emptyName}`);
}
console.log('\nAnalysis complete.');
