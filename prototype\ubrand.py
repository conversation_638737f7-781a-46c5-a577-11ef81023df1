import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# Mapping dictionary: standardized manufacturer name to list of variations and code

brand_mapping = {
    "Dentsply Sirona": {
        "variations": [
            "Dentsply Sirona Preventive", "Dentsply Sirona Midwest", "Dentsply Sirona Restorative",
            "Dentsply Maillefer", "Dentsply Pharmaceutical", "Dentsply-Ceramco", "Dentsply Trubyte",
            "Dentsply Professional", "Dentsply", "Dentsply Rinn", "Dentsply International", 
            "Dentsply Endodontics", "Sirona", "Sirona Dental Systems Llc", "Caulk", "Ceramco"
        ],
        "code": "DSR"
    },
    "Kerr": {
        "variations": [
            "Kerr MFG Co", "Kerr Lab Division", "Kerr Dental", "Kerr Corporation", 
            "Kerr Endodontics", "Kerr Rotary (Formerly Axis)", "KerrLab", "Kerr TotalCare"
        ],
        "code": "KER"
    },
    "Solventum": {
        "variations": [
            "Solventum,former 3M HealthCare", "3M (Solventum)", "3M", "3M Medical Products", 
            "3M Business Products", "3M Occupational Health & Env", "Solventum Corporation"
        ],
        "code": "SOL"
    },
    "GC America": {
        "variations": ["GC America", "GC America, Inc.", "GCORTHO"],
        "code": "GCA"
    },
    "Ivoclar Vivadent": {
        "variations": ["Ivoclar Vivadent, Inc", "Ivoclar", "Vivadent"],
        "code": "IVO"
    },
    "Kulzer": {
        "variations": ["Kulzer, Inc.", "Kulzer"],
        "code": "KUL"
    },
    "Septodont": {
        "variations": ["Septodont", "Cook-Waite", "Orabloc"],
        "code": "SEP"
    },
    "Hu-Friedy": {
        "variations": ["Hu-Friedy", "Hufriedy"],
        "code": "HUF"
    },
    "Coltene": {
        "variations": ["Coltene Inc", "Coltene/Whaledent", "Coltene Whaledent"],
        "code": "COL"
    },
    "Shofu": {
        "variations": ["Shofu Dental Corp", "Shofu Dental Corp Lab", "Shofu", "SHOFU"],
        "code": "SHO"
    },
    "SS White": {
        "variations": ["S S White Dental Inc.", "SS White", "SSW Burs"],
        "code": "SSW"
    },
    "Meisinger": {
        "variations": ["Meisinger USA", "Meisinger"],
        "code": "MEI"
    },
    "Bien Air": {
        "variations": ["BIEN AIR USA INC", "Bien Air USA", "Bien Air"],
        "code": "BIE"
    },
    "KaVo": {
        "variations": ["Kavo America Dental Corp.", "KaVo", "Kavo"],
        "code": "KAV"
    },
    "NSK": {
        "variations": ["Nsk America Corp", "NSK"],
        "code": "NSK"
    },
    "W&H": {
        "variations": ["W&H Impex Inc", "W&H DENTALWERK BUERMOSS", "W&H", "A-dec/W&H"],
        "code": "WNH"
    },
    "Henry Schein": {
        "variations": [
            "Henry Schein Inc.", "Henry Schein Custom", "Henry Schein Orthodontics", 
            "Henry Schein Training", "Henry Schein Promotions", "Schein Catalogs & Premium"
        ],
        "code": "HSC"
    },
    "Benco Dental": {
        "variations": ["BENCO", "Benco Dental", "BencoNET"],
        "code": "BEN"
    },
    "Patterson Dental": {
        "variations": ["Patterson Dental Supply", "Patterson"],
        "code": "PAT"
    },
    "Plasdent": {
        "variations": ["Plasdent Corp.", "Plasdent"],
        "code": "PLA"
    },
    "Premier Dental": {
        "variations": ["Premier Dental", "Premier Dental Products"],
        "code": "PRE"
    },
    "Angelus": {
        "variations": ["Angelus Produtos Odonotologico", "Angelus Dental", "ANGELUS"],
        "code": "ANG"
    },
    "Practicon": {
        "variations": ["Practicon"],
        "code": "PRA"
    },
    "Nordent": {
        "variations": ["Nordent Mfg", "Nordent"],
        "code": "NOR"
    },
    "PDT": {
        "variations": ["PDT, Inc", "PDT"],
        "code": "PDT"
    },
    "A. Titan Instruments": {
        "variations": ["A. Titan Instruments", "A Titan"],
        "code": "ATI"
    },
    "J&J Instruments": {
        "variations": ["J&J Instruments Inc.", "J&J Instruments"],
        "code": "JJI"
    },
    "Mada Medical": {
        "variations": ["Mada Medical Products Inc", "Mada Medical"],
        "code": "MAD"
    },
    "Ace Surgical": {
        "variations": ["Ace Surgical", "Ace Surgical Supply", "ACE"],
        "code": "ACE"
    },
    "Vista Apex": {
        "variations": ["Vista Apex", "Vista Dental", "Vista Dental Products"],
        "code": "VIS"
    },
    "Directa": {
        "variations": ["Directa Inc.", "Directa", "Directa Dental"],
        "code": "DIR"
    },
    "Carestream": {
        "variations": ["Carestream Health Inc", "Carestream", "Kodak"],
        "code": "CAR"
    },
    "Pierrel": {
        "variations": ["Pierrel S.P.A.", "Pierrel Pharma"],
        "code": "PIE"
    },
    "Zirc Dental": {
        "variations": ["Zirc Dental Products", "Zirc"],
        "code": "ZIR"
    },
    "MicroCare": {
        "variations": ["MicroCare LLC", "MicroCare (Formerly Certol International)", "Certol International"],
        "code": "MIC"
    },
    "Hager": {
        "variations": ["Hager Worldwide Inc", "Hager"],
        "code": "HAG"
    },
    "Cardinal Health": {
        "variations": ["Cardinal Health", "Cardinal Health Generic", "Cardinal Health, VBI Vaccines"],
        "code": "CRD"
    },
    "Medmix": {
        "variations": ["Medmix US Inc.", "MEDMIX"],
        "code": "MDX"
    },
    "Centrix": {
        "variations": ["Centrix Incorporated", "Centrix Dental", "Centrix"],
        "code": "CEN"
    },
    "Harry J Bosworth": {
        "variations": ["Harry J Bosworth Co", "Bosworth", "Bosworth Co"],
        "code": "HJB"
    },
    "DMG America": {
        "variations": ["DMG America", "DMG"],
        "code": "DMG"
    },
    "Beutlich": {
        "variations": ["Beutlich Inc"],
        "code": "BEU"
    },
    "Cetylite": {
        "variations": ["Cetylite Industries Inc"],
        "code": "CET"
    },
    "Gingi-Pak": {
        "variations": ["Gingi-Pak Pharmaceutical", "Gingi-Pak"],
        "code": "GIN"
    },
    "Pac-Dent": {
        "variations": ["Pac-Dent, Inc", "Pacdent", "Pacdent"],
        "code": "PAC"
    },
    "Sultan": {
        "variations": ["Sultan Healthcare Inc", "Sultan"],
        "code": "SUL"
    },
    "RAM Products": {
        "variations": ["RAM Products, Inc.", "Ram Products"],
        "code": "RAM"
    },
    "Dynaflex": {
        "variations": ["Dynaflex"],
        "code": "DYN"
    },
    "Ortho Technology": {
        "variations": ["Ortho Technology"],
        "code": "ORT"
    },
    "Grobet": {
        "variations": ["Grobet File Co Of America", "Grobet USA"],
        "code": "GRB"
    },
    "Palmero": {
        "variations": ["Palmero Sales Co Inc", "Palmero"],
        "code": "PAL"
    },
    "Foredom": {
        "variations": ["Foredom Electric Co.", "Foredom Electric"],
        "code": "FOR"
    },
    "Acteon": {
        "variations": ["Acteon Inc", "Acteon"],
        "code": "ACT"
    },
    "Buffalo Dental": {
        "variations": ["Buffalo Dental Mfg Co Inc", "Buffalo"],
        "code": "BUF"
    },
    "Quality Aspirators": {
        "variations": ["Quality Aspirators"],
        "code": "QUA"
    },
    "Sintec Dental": {
        "variations": ["Sintec Dental"],
        "code": "SIN"
    },
    "Dedeco": {
        "variations": ["Dedeco International Inc", "Dedeco"],
        "code": "DED"
    },
    "Amann Girrbach": {
        "variations": ["Amann Girrbach America", "Amann Girrbach", "Amann Girrbach Ag"],
        "code": "AMG"
    },
    "Microcopy": {
        "variations": ["Microcopy", "Microcopy Dental"],
        "code": "MCP"
    },
    "Edge Endo": {
        "variations": ["Edge Endo LLC"],
        "code": "EDG"
    },
    "Pfingst": {
        "variations": ["Pfingst & Comp Inc", "Pfingst"],
        "code": "PFN"
    },
    "Edenta": {
        "variations": ["Edenta AG"],
        "code": "EDE"
    },
    "Parkell": {
        "variations": ["Parkell"],
        "code": "PAR"
    },
    "Danville Materials": {
        "variations": ["Danville Materials, Inc", "Danville Materials"],
        "code": "DAN"
    },
    "Spring Health": {
        "variations": ["Spring Health Products"],
        "code": "SPR"
    },
    "Garreco": {
        "variations": ["Garreco LLC", "Garreco"],
        "code": "GAR"
    },
    "Dental Ventures": {
        "variations": ["Dental Ventures Of Americ", "Dental Ventures"],
        "code": "DVE"
    },
    "Chung Sung Wing": {
        "variations": ["Chung Sung Wing"],
        "code": "CSW"
    },
    "Insight Endo": {
        "variations": ["Insight Endo"],
        "code": "INS"
    },
    "Micro Essential Labs": {
        "variations": ["Micro Essential Labs Inc"],
        "code": "MEL"
    },
    "Integral Systems": {
        "variations": ["Integral Systems Inc"],
        "code": "INT"
    },
    "Thermoplastic Comfort Sys": {
        "variations": ["Thermoplastic Comfort Sys"],
        "code": "TCS"
    },
    "Valplast": {
        "variations": ["Valplast International", "Valplast"],
        "code": "VAL"
    },
    "Itena": {
        "variations": ["Itena North America", "Itena USA", "iTena"],
        "code": "ITE"
    },
    "Brasseler": {
        "variations": ["Brasseler USA Inc"],
        "code": "BRA"
    },
    "Ellman": {
        "variations": ["Ellman Intl Mfg Inc", "ELLMAN"],
        "code": "ELL"
    },
    "Clinicians Choice": {
        "variations": ["Clinicians Choice Dental", "Clinicians Choice"],
        "code": "CLI"
    },
    "StarDental": {
        "variations": ["StarDental Co.", "Star DentalEz", "StarDental/DentalEZ"],
        "code": "STD"
    },
    "American Dental Supply": {
        "variations": ["American Dental Supply"],
        "code": "ADS"
    },
    "Den-Mat": {
        "variations": ["Den-mat", "DenMat"],
        "code": "DEN"
    },
    "Mydent": {
        "variations": ["Mydent", "Mydent Research Corp.", "Mydent (Defend)", "Defend"],
        "code": "MYD"
    },
    "Corning Rubber": {
        "variations": ["Corning Rubber Co Inc"],
        "code": "COR"
    },
    "Renfert": {
        "variations": ["Renfert USA", "Renfert"],
        "code": "REN"
    },
    "Select Dental": {
        "variations": ["Select Dental", "Select Dental Mfg"],
        "code": "SEL"
    },
    "Zest Anchors": {
        "variations": ["Zest Anchors, Llc", "Zest Anchors"],
        "code": "ZES"
    },
    "Yates Motloid": {
        "variations": ["Yates Motloid", "Yates-Motloid"],
        "code": "YAT"
    },
    "Vita": {
        "variations": ["Vita North America", "Vita"],
        "code": "VIT"
    },
    "VOCO": {
        "variations": ["VOCO GmbH", "VOCO"],
        "code": "VOC"
    },
    "Kuraray": {
        "variations": ["Kuraray America Inc", "Kuraray"],
        "code": "KUR"
    },
    "Planmeca": {
        "variations": ["Planmeca"],
        "code": "PLM"
    },
    "Indenco": {
        "variations": ["Indenco Dental Products"],
        "code": "IND"
    },
    "Dentaco": {
        "variations": ["Dentaco GmbH"],
        "code": "DNC"
    },
    "VH Technologies": {
        "variations": ["VH Technologies Ltd", "VH Technologies"],
        "code": "VHT"
    },
    "Park Istre": {
        "variations": ["Park Istre LLC"],
        "code": "PIS"
    },
    "Ortho Organizers": {
        "variations": ["Ortho Organizers, Inc.", "Ortho Organizers"],
        "code": "ORG"
    },
    "Essential Dental": {
        "variations": ["Essential Dental Systems"],
        "code": "ESS"
    },
    "Lang Dental": {
        "variations": ["Lang Dental Mfg Co Inc", "Lang Dental"],
        "code": "LAN"
    },
    "Direct Crown": {
        "variations": ["Direct Crown"],
        "code": "DCR"
    },
    "E C Moore": {
        "variations": ["E C Moore Co Inc", "EC Moore"],
        "code": "ECM"
    },
    "Pentron": {
        "variations": ["Pentron Clinical", "PENTRON"],
        "code": "PEN"
    },
    "American Consolidated": {
        "variations": ["American Consolidated Mfg"],
        "code": "ACM"
    },
    "NuRadiance": {
        "variations": ["NuRadiance, Inc", "Nu Radiance"],
        "code": "NUR"
    },
    "Pulpdent": {
        "variations": ["Pulpdent Corporation", "Pulpdent"],
        "code": "PUL"
    },
    "Ma De Tech": {
        "variations": ["Ma De Tech"],
        "code": "MAT"
    },
    "Orthopli": {
        "variations": ["Orthopli Corp", "Orthopli"],
        "code": "ORP"
    },
    "Reliance Dental": {
        "variations": ["Reliance Dental Mfg Inc", "Reliance Dental Manufacturing"],
        "code": "REL"
    },
    "Roydent": {
        "variations": ["Roydent Dental Products"],
        "code": "ROY"
    },
    "Denovo": {
        "variations": ["Denovo"],
        "code": "DEV"
    },
    "Temrex": {
        "variations": ["Temrex", "Temrex Corp."],
        "code": "TEM"
    },
    "Scientific Pharmaceutical": {
        "variations": ["Scientific Pharmaceutical"],
        "code": "SCI"
    },
    "SDI": {
        "variations": ["SDI (North America), Inc.", "SDI"],
        "code": "SDI"
    },
    "WaterPik": {
        "variations": ["WaterPik, Inc", "WaterPik", "Waterpik"],
        "code": "WAT"
    },
    "Pascal": {
        "variations": ["Pascal Co Inc", "Pascal"],
        "code": "PAS"
    },
    "Roescheisen": {
        "variations": ["Roescheisen Gmbh & Co"],
        "code": "ROE"
    },
    "Macan Genesis": {
        "variations": ["Macan Genesis Mfg Llc"],
        "code": "MAC"
    },
    "DCI": {
        "variations": ["DCI International", "DCI"],
        "code": "DCI"
    },
    "Tuttnauer": {
        "variations": ["Tuttnauer USA Co.", "Tuttnauer"],
        "code": "TUT"
    },
    "Midmark": {
        "variations": ["Midmark Corporation", "Midmark", "Progeny A Midmark Company"],
        "code": "MID"
    },
    "Forest": {
        "variations": ["Forest Dental Equipment", "Forest"],
        "code": "FST"
    },
    "Royal Dental": {
        "variations": ["Royal Dental Mfg Inc"],
        "code": "ROD"
    },
    "Conmed": {
        "variations": ["Conmed Linvatec", "Conmed Endosurgery"],
        "code": "CON"
    },
    "Velopex": {
        "variations": ["Velopex International Inc", "Velopex"],
        "code": "VEL"
    },
    "Purekeys": {
        "variations": ["Purekeys, Inc"],
        "code": "PUR"
    },
    "Vatech": {
        "variations": ["Vatech America, Inc", "Vatech America"],
        "code": "VAT"
    },
    "Air Techniques": {
        "variations": ["Air Techniques Inc", "Air Techniques"],
        "code": "AIR"
    },
    "Digital Doc": {
        "variations": ["Digital Doc, Inc", "Digi-Doc"],
        "code": "DIG"
    },
    "Carina": {
        "variations": ["Carina"],
        "code": "CRN"
    },
    "3Shape": {
        "variations": ["3Shape Inc.", "3SHAPE"],
        "code": "THS"
    },
    "Dental Imaging": {
        "variations": ["Dental Imaging Technlgies", "Dental Imaging"],
        "code": "DII"
    },
    "Sota": {
        "variations": ["Sota Precision Optics", "SOTA Imaging"],
        "code": "SOT"
    },
    "Adec": {
        "variations": ["Adec Inc", "A-dec"],
        "code": "ADE"
    },
    "Firstar": {
        "variations": ["Firstar Dental Company"],
        "code": "FIS"
    },
    "Tri-Tech": {
        "variations": ["Tri-Tech Medical Inc."],
        "code": "TRI"
    },
    "Den-Tal-Ez": {
        "variations": ["Den-Tal-Ez, Inc.", "DentalEz", "DentalEZ Group"],
        "code": "DTE"
    },
    "Solmetex": {
        "variations": ["Solmetex"],
        "code": "SLM"
    },
    "Basevac": {
        "variations": ["Basevac Dental R.s. Mrrsn"],
        "code": "BAS"
    },
    "Columbia Dentoform": {
        "variations": ["Columbia Dentoform Corp."],
        "code": "CDU"
    },
    "Accutron": {
        "variations": ["Accutron, Inc."],
        "code": "ACC"
    },
    "Modular & Custom": {
        "variations": ["Modular & Custom Cabinets"],
        "code": "MOD"
    },
    "LABO AMERICA": {
        "variations": ["LABO AMERICA, INC"],
        "code": "LAB"
    },
    "Porter": {
        "variations": ["Porter Instrument Div.", "Porter Instrument Co Inc", "Porter"],
        "code": "POR"
    },
    "L&R": {
        "variations": ["L&R Mfg Co", "L&R", "L&R Ultrasonics"],
        "code": "LRM"
    },
    "Surgically Clean Air": {
        "variations": ["Surgically Clean Air Inc"],
        "code": "SCA"
    },
    "Ramvac": {
        "variations": ["Ramvac", "RamVac"],
        "code": "RMV"
    },
    "Light Instruments": {
        "variations": ["Light Instruments USA LLC"],
        "code": "LIG"
    },
    "Phoenix Imaging": {
        "variations": ["Phoenix Imaging, Corp"],
        "code": "PHI"
    },
    "Pureway": {
        "variations": ["Pureway Total Compliance", "PureWay"],
        "code": "PWT"
    },
    "Radic8": {
        "variations": ["Radic8 Ltd"],
        "code": "RAD"
    },
    "Blue Chip": {
        "variations": ["Blue Chip Medical Prod"],
        "code": "BLC"
    },
    "Lucas Lifecare": {
        "variations": ["Lucas Lifecare"],
        "code": "LUC"
    },
    "Kinetic": {
        "variations": ["Kinetic Instruments Inc"],
        "code": "KIN"
    },
    "Richmond": {
        "variations": ["Richmond Dental Company", "Richmond"],
        "code": "RIC"
    },
    "Medline": {
        "variations": ["Medline Industries Inc", "Medline", "ComfortEase by Medline"],
        "code": "MED"
    },
    "Dukal": {
        "variations": ["Dukal LLC", "DUKAL Corporation", "Dukal"],
        "code": "DUK"
    },
    "Microbrush": {
        "variations": ["Microbrush Corp", "Microbrush Corporation", "Microbrush"],
        "code": "MBR"
    },
    "Essentials Healthcare": {
        "variations": ["Essentials Healthcare"],
        "code": "EHC"
    },
    "Medicom": {
        "variations": ["Medicom"],
        "code": "MCM"
    },
    "First Aid Bandage": {
        "variations": ["First Aid Bandage Co"],
        "code": "FAI"
    },
    "Deroyal": {
        "variations": ["Deroyal Industries Inc"],
        "code": "DER"
    },
    "Dental Health": {
        "variations": ["Dental Health Products"],
        "code": "DHP"
    },
    "Crosstex": {
        "variations": ["Crosstex International", "Crosstex"],
        "code": "CRO"
    },
}

# Function to find standardized brand and code
def get_standardized_brand_and_code(brand):
    if not isinstance(brand, str):
        return "UNKNOWN", "UNKNOWN"  # Handle non-string or missing brand
    for std_name, info in brand_mapping.items():
        if brand in info["variations"]:
            return std_name, info["code"]
    return brand, "UNKNOWN"  # Return original brand and UNKNOWN if not found

# Function to process JSON files
def process_json_file(input_file, output_file):
    try:
        # Read JSON file with UTF-8 encoding
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_products = len(data)
        processed_products = 0
        
        # Process each item in the JSON data
        for item in data:
            try:
                if 'brand' in item:
                    ubrand, code = get_standardized_brand_and_code(item['brand'])
                    item['ubrand'] = ubrand
                    item['manufacturer_code'] = code
                    processed_products += 1
                else:
                    item['ubrand'] = "UNKNOWN"
                    item['manufacturer_code'] = "UNKNOWN"
                    processed_products += 1
            except Exception as e:
                logging.error(f"Error processing item in {input_file}: {str(e)}")
        
        # Write updated data to output file with UTF-8 encoding
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logging.info(f"Processed {input_file}: Total products = {total_products}, Products with ubrand created = {processed_products}")
    
    except Exception as e:
        logging.error(f"Error processing {input_file}: {str(e)}")

# List of input and output files
files = [
    ("henry.json", "henry_processed.json"),
    ("benco.json", "benco_processed.json"),
    ("frontier.json", "frontier_processed.json"),
    ("dds.json", "dds_processed.json"),
    ("optimus.json", "optimus_processed.json")
]

# Process each file
for input_file, output_file in files:
    process_json_file(input_file, output_file)