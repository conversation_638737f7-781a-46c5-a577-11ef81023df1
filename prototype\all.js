const fs = require('fs');

// Read and parse the JSON files
const bencoData = JSON.parse(fs.readFileSync('benco.json', 'utf8'));
const henryData = JSON.parse(fs.readFileSync('henry.json', 'utf8'));
const frontierData = JSON.parse(fs.readFileSync('frontier.json', 'utf8'));
const ddsData = JSON.parse(fs.readFileSync('dds.json', 'utf8'));
const optimusData = JSON.parse(fs.readFileSync('optimus.json', 'utf8'));

// Create a Map to hold the merged data
const mergedData = new Map();

// Function to process each file's data
function processFile(data, seller) {
    data.forEach(item => {
        const mpn = item.mpn;
        if (!mergedData.has(mpn)) {
            mergedData.set(mpn, []);
        }
        const product = { ...item, seller: seller };
        mergedData.get(mpn).push(product);
    });
}

// Process each file's data
processFile(bencoData, 'benco');
processFile(henryData, 'henry');
processFile(frontierData, 'frontier');
processFile(ddsData, 'dds');
processFile(optimusData, 'optimus');

// Convert the Map to the desired output format
const result = [];
for (const [mpn, products] of mergedData) {
    result.push({ mpn: mpn, products: products });
}

// Write the result to all.json
fs.writeFileSync('all.json', JSON.stringify(result, null, 2));