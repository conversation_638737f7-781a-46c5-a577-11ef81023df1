const puppeteer = require('puppeteer');
const fs = require('fs').promises; // Use fs.promises for async file operations
const readline = require('readline');
const path = require('path');

// Define paths for input and output files
const BASE_URL = "https://shop.benco.com"; // Still good to have for context, though not directly used for navigation anymore
const bencoJsonPath = path.resolve(__dirname, 'benco.json');
const bencoUpdatedJsonPath = path.resolve(__dirname, 'benco_updated.json'); // New path for the updated file

// User data and progress file paths
const userDataDir = path.resolve("./user_data");

// Function to prompt the user for input in the terminal
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

// Function to load JSON file (async)
async function loadJsonFile(filePath, defaultValue = []) {
    try {
        const fileContent = await fs.readFile(filePath, 'utf8');
        return JSON.parse(fileContent);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.warn(`File not found: ${filePath}. Returning default value.`);
            return defaultValue;
        }
        console.error(`Error reading ${filePath}:`, error);
        return defaultValue;
    }
}

// Function to save JSON file (async)
async function saveJsonFile(filePath, data) {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
}

/**
 * Helper function to retry an asynchronous operation on TimeoutError.
 * @param {Function} operation - The asynchronous function to execute.
 * @param {number} maxRetries - Maximum number of retries.
 * @param {number} delayMs - Delay in milliseconds between retries.
 * @returns {Promise<any>} The result of the operation.
 * @throws {Error} If the operation fails after all retries or for a non-TimeoutError.
 */
async function retryOperation(operation, maxRetries = 3, delayMs = 5000) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
                console.warn(`Operation timed out (attempt ${i + 1}/${maxRetries + 1}). Retrying in ${delayMs / 1000} seconds...`);
                if (i < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delayMs)); // Wait before retrying
                } else {
                    throw new Error(`Operation failed after ${maxRetries + 1} attempts due to TimeoutError for URL: ${operation.url || 'unknown'}.`);
                }
            } else {
                throw error; // Re-throw other types of errors immediately
            }
        }
    }
}

async function updateMpnDetails() {
    let browser;
    // Ensure user data directory exists
    await fs.mkdir(userDataDir, { recursive: true }).catch(console.error);

    try {
        // 1. Copy benco.json to benco_updated.json
        console.log(`Copying ${bencoJsonPath} to ${bencoUpdatedJsonPath}...`);
        let bencoData;
        try {
            const originalContent = await fs.readFile(bencoJsonPath, 'utf8');
            bencoData = JSON.parse(originalContent);
            await fs.writeFile(bencoUpdatedJsonPath, JSON.stringify(bencoData, null, 2), 'utf8');
            console.log('File copied successfully.');
        } catch (error) {
            console.error(`Error copying benco.json: ${error.message}`);
            return; // Exit if copy fails
        }

        // Load benco_updated.json
        let productsToProcess = bencoData; // Now work with the data from benco_updated.json

        // Filter objects with null mpn and brand "BENCO"
        const filteredProducts = productsToProcess.filter(obj =>
            obj.mpn === null && obj.brand === "BENCO"
        );

        if (filteredProducts.length === 0) {
            console.log('No objects found with null MPN and brand "BENCO". Exiting.');
            return;
        }

        console.log(`Found ${filteredProducts.length} objects with null MPN and brand "BENCO" to re-scrape.`);

        console.log(`Launching browser with user data directory: ${userDataDir}`);
        browser = await puppeteer.launch({
            headless: false, // Set to true for background execution
            userDataDir: userDataDir, // This will save/load your session
            args: [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--start-maximized"
            ],
            defaultViewport: null
        });
        const page = await browser.newPage();
        page.setDefaultNavigationTimeout(60000); // 60 seconds timeout for navigation

        // Initial navigation and manual login prompt
        console.log('Navigating to base URL for potential login: ' + BASE_URL);
        await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
        console.log('Page loaded. Please log in manually in the browser if required.');
        await askQuestion('Press Enter in this terminal AFTER you have successfully logged in to the website and the page has loaded. ');
        console.log('Login confirmed. Continuing with MPN scraping...');

        let updatedCount = 0;
        let missedMpnUrls = [];
        const BATCH_SIZE = 50; // Save every 50 products

        for (let i = 0; i < filteredProducts.length; i++) {
            const product = filteredProducts[i];
            const fullurl = product.url; // Use the already complete URL from the object

            if (!fullurl) {
                console.warn(`Product at index ${i} has no 'url'. Skipping.`);
                continue;
            }

            console.log(`Processing ${i + 1}/${filteredProducts.length}: ${fullurl}`);

            try {
                // Navigate to product page with retry logic
                await retryOperation(async () => {
                    await page.goto(fullurl, { waitUntil: 'domcontentloaded', timeout: 60000 });
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Small delay for content to render
                }, 3, 5000);

                // Scrape only the MPN
                const scrapedMpn = await page.evaluate(() => {
                    const mpnElement = document.querySelector('div.product-detail-wrapper span[itemprop="sku"]');
                    return mpnElement ? mpnElement.innerText.trim() : null;
                });

                // Find the original object in the `productsToProcess` array and update its MPN
                const originalProductIndex = productsToProcess.findIndex(p => p.url === product.url);
                if (originalProductIndex !== -1) {
                    if (scrapedMpn) {
                        productsToProcess[originalProductIndex].mpn = scrapedMpn;
                        updatedCount++;
                        console.log(`  Updated MPN for ${product.name || product.url}: ${scrapedMpn}`);
                    } else {
                        console.warn(`  MPN element not found for ${product.name || product.url}. MPN remains null.`);
                        missedMpnUrls.push(fullurl);
                    }
                } else {
                    console.warn(`  Original product not found in the main data array for URL: ${fullurl}`);
                }

                // Save incrementally every BATCH_SIZE products
                if ((i + 1) % BATCH_SIZE === 0 || (i + 1) === filteredProducts.length) {
                    console.log(`Saving progress to ${bencoUpdatedJsonPath} after ${updatedCount} updates...`);
                    await saveJsonFile(bencoUpdatedJsonPath, productsToProcess);
                }

            } catch (error) {
                console.error(`Failed to scrape MPN for '${fullurl}' after retries. Skipping. Error: ${error.message}`);
                missedMpnUrls.push(fullurl);
            }
        }

        console.log('\n--- MPN scraping and updating completed ---');
        console.log(`Total MPNs updated: ${updatedCount}`);
        if (missedMpnUrls.length > 0) {
            console.log(`Failed to scrape MPN for ${missedMpnUrls.length} URLs. These were:`);
            missedMpnUrls.forEach(url => console.log(`- ${url}`));
            // Optionally save these missed URLs to a file if needed
            await saveJsonFile(path.resolve(__dirname, 'missed_mpn_urls.json'), missedMpnUrls);
            console.log(`Missed MPN URLs saved to missed_mpn_urls.json`);
        }

        // Log final counts for null mpn and brand "BENCO" and null mpn and null price
        let finalNullMpnBencoBrandCount = 0;
        let finalNullMpnNullPriceCount = 0;

        productsToProcess.forEach(obj => {
            if (obj.mpn === null && obj.brand === "BENCO") {
                finalNullMpnBencoBrandCount++;
            }
            if (obj.mpn === null && obj.price === null) {
                finalNullMpnNullPriceCount++;
            }
        });

        console.log(`\nFinal count of objects with null 'mpn' and 'brand' as "BENCO": ${finalNullMpnBencoBrandCount}`);
        console.log(`Final count of objects with null 'mpn' and null 'price': ${finalNullMpnNullPriceCount}`);


    } catch (error) {
        console.error('An unhandled error occurred during processing:', error);
    } finally {
        if (browser) {
            await browser.close();
            console.log('Browser closed.');
        }
        rl.close();
    }
}

// Run the update process
updateMpnDetails();