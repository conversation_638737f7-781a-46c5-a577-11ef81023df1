// finalPriceComparison.js

const fs = require('fs');
const path = require('path');

const unifiedUpdatedFilePath = 'unified_updated.json';
const allSellers = ['henry', 'benco', 'frontier', 'dds', 'optimus'];

/**
 * Parses a price string to extract the numeric value.
 * Handles formats like "$75.59", "1 @ $75.59", "$1,081.79", or "53.17".
 * It correctly handles commas within the numeric part.
 * @param {string} priceString - The price string to parse.
 * @returns {number} The extracted numeric price, or NaN if not found.
 */
function parsePrice(priceString) {
    if (typeof priceString !== 'string') {
        return NaN;
    }

    let extractedNumericString;

    // First, try to find a price after a '$' symbol
    // This regex specifically looks for '$' followed by digits (with optional commas and decimals)
    const dollarPriceMatch = priceString.match(/\$([\d,]+\.?\d*)/);

    if (dollarPriceMatch && dollarPriceMatch[1]) {
        // If a price after '$' is found, use that captured group
        extractedNumericString = dollarPriceMatch[1];
    } else {
        // If no '$' price is found, assume the entire string (after trimming) is the numeric value
        // This handles cases like "53.17"
        extractedNumericString = priceString.trim();
    }

    // Remove all commas from the extracted numeric string before parsing to float
    const cleanedNumericString = extractedNumericString.replace(/,/g, '');

    const priceValue = parseFloat(cleanedNumericString);

    return !isNaN(priceValue) ? priceValue : NaN;
}

/**
 * Finds 50 products meeting specific criteria from unified_updated.json,
 * then calculates and compares total prices under different buying scenarios.
 */
function performFinalAnalysis() {
    let unifiedUpdatedData;

    console.log(`\n--- Starting final price comparison analysis ---`);

    try {
        const fileContent = fs.readFileSync(unifiedUpdatedFilePath, 'utf8');
        unifiedUpdatedData = JSON.parse(fileContent);
    } catch (error) {
        console.error(`Error reading or parsing ${unifiedUpdatedFilePath}:`, error.message);
        console.log('Please ensure "unified_updated.json" exists in the same directory and is valid JSON.');
        return;
    }

    if (!Array.isArray(unifiedUpdatedData)) {
        console.error(`Error: The root of ${unifiedUpdatedFilePath} must be an array of objects.`);
        return;
    }

    // Condition 1: Filter out products that have duplicated MPN from the same seller.
    // This means, for each seller, _sellerProductCounts[sellerName] must be 1.
    const filteredProducts = unifiedUpdatedData.filter(obj => {
        // Ensure _sellerProductCounts exists and is an object
        if (!obj._sellerProductCounts || typeof obj._sellerProductCounts !== 'object') {
            return false; // Exclude if this crucial property is missing or malformed
        }

        // Check if any seller has contributed more than one product for this MPN
        for (const sellerName in obj._sellerProductCounts) {
            if (obj._sellerProductCounts[sellerName] > 1) {
                return false; // Exclude this object as it has duplicates from the same seller
            }
        }
        return true; // Include if no seller has contributed more than one product for this MPN
    });

    console.log(`Products after filtering for no same-seller MPN duplicates: ${filteredProducts.length}`);

    // Categorize products by minmax ranges
    const products90_100 = []; // minmax >= 90 and minmax <= 100
    const products80_90 = [];  // minmax >= 80 and minmax < 90
    const products60_80 = [];  // minmax >= 60 and minmax < 80

    filteredProducts.forEach(obj => {
        if (typeof obj.minmax === 'number' && !isNaN(obj.minmax)) {
            if (obj.minmax >= 90 && obj.minmax <= 100) {
                products90_100.push(obj);
            } else if (obj.minmax >= 80 && obj.minmax < 90) {
                products80_90.push(obj);
            } else if (obj.minmax >= 60 && obj.minmax < 80) {
                products60_80.push(obj);
            }
        }
    });

    console.log(`Products in 90-100 range: ${products90_100.length}`);
    console.log(`Products in 80-90 range: ${products80_90.length}`);
    console.log(`Products in 60-80 range: ${products60_80.length}`);

    // Sort each category by minmax in descending order
    products90_100.sort((a, b) => b.minmax - a.minmax);
    products80_90.sort((a, b) => b.minmax - a.minmax);
    products60_80.sort((a, b) => b.minmax - a.minmax);

    // Select the top products for each category
    const top20_90_100 = products90_100.slice(0, 20);
    const top20_80_90 = products80_90.slice(0, 20);
    const top10_60_80 = products60_80.slice(0, 10);

    // Combine into the final list of 50 products
    const top50Products = [...top20_90_100, ...top20_80_90, ...top10_60_80];

    // Shuffle the combined list to mix them up for display if desired, or keep ordered by range
    // For consistency with problem statement, we'll keep them ordered by range then minmax
    // If fewer than 50 products are found, the list will be smaller.
    console.log(`Selected total products for analysis: ${top50Products.length}`);

    if (top50Products.length === 0) {
        console.log('No products found matching the specified criteria to perform price comparison.');
        return;
    }

    // --- Detailed Price Information for Each of the 50 Products ---
    console.log('\n--- Detailed Price Information for Selected Products ---');
    top50Products.forEach((product, index) => {
        const prices = [];
        const sellerPrices = {}; // { sellerName: price } for this product

        for (const key in product) {
            if (key.endsWith('_price') || key.match(/_price\d+$/)) {
                const priceValue = parsePrice(product[key]);
                if (!isNaN(priceValue)) {
                    prices.push(priceValue);
                    // Extract seller name from key (e.g., "henry_price" -> "henry")
                    const sellerNameMatch = key.match(/^([a-zA-Z]+)_price/);
                    if (sellerNameMatch && sellerNameMatch[1]) {
                        // Store the price associated with the seller. If multiple prices from same seller, take the min
                        const currentSeller = sellerNameMatch[1];
                        sellerPrices[currentSeller] = Math.min(sellerPrices[currentSeller] || Infinity, priceValue);
                    }
                }
            }
        }

        let minPrice = Infinity;
        let minPriceSellers = [];
        let maxPrice = -Infinity;

        if (prices.length > 0) {
            minPrice = Math.min(...prices);
            maxPrice = Math.max(...prices);

            // Find sellers offering the min price
            for (const seller in sellerPrices) {
                if (sellerPrices[seller] === minPrice) {
                    minPriceSellers.push(seller);
                }
            }
        }

        console.log(`${index + 1}. MPN: ${product.mpn}, MinMax: ${product.minmax.toFixed(2)}`);
        console.log(`   Cheapest Price: $${minPrice.toFixed(2)} (from: ${minPriceSellers.join(', ')})`);
        console.log(`   Maximum Price: $${maxPrice.toFixed(2)}`);
    });


    // --- Calculate Total Price: Cheapest Option for Each Product ---
    let cheapestOptionTotalPrice = 0;
    top50Products.forEach(product => {
        const prices = [];
        for (const key in product) {
            if (key.endsWith('_price') || key.match(/_price\d+$/)) {
                const priceValue = parsePrice(product[key]);
                if (!isNaN(priceValue)) {
                    prices.push(priceValue);
                }
            }
        }
        if (prices.length > 0) {
            cheapestOptionTotalPrice += Math.min(...prices);
        }
    });

    // --- Calculate Total Price: Bought from One Seller Only (for products they supply) ---
    const sellerTotals = {};
    const sellerProductsSuppliedCount = {};
    const sellerProductList = {}; // To store actual products supplied by each seller

    allSellers.forEach(seller => {
        sellerTotals[seller] = 0;
        sellerProductsSuppliedCount[seller] = 0;
        sellerProductList[seller] = [];
    });

    top50Products.forEach(product => {
        allSellers.forEach(sellerName => {
            let minPriceFromThisSeller = Infinity;
            let foundPrice = false;

            // Find all price properties for the current seller in this product object
            for (const key in product) {
                if (key.startsWith(`${sellerName}_price`)) {
                    const priceValue = parsePrice(product[key]);
                    if (!isNaN(priceValue)) {
                        minPriceFromThisSeller = Math.min(minPriceFromThisSeller, priceValue);
                        foundPrice = true;
                    }
                }
            }

            if (foundPrice && minPriceFromThisSeller !== Infinity) {
                sellerTotals[sellerName] += minPriceFromThisSeller;
                sellerProductsSuppliedCount[sellerName]++;
                sellerProductList[sellerName].push(product); // Add the product to this seller's list
            }
        });
    });

    let bestSingleSellerTotal = Infinity;
    let bestSingleSellerName = 'N/A';
    let bestSingleSellerProductsCount = 0;

    allSellers.forEach(sellerName => {
        if (sellerProductsSuppliedCount[sellerName] > 0) {
            if (sellerTotals[sellerName] < bestSingleSellerTotal ||
                (sellerTotals[sellerName] === bestSingleSellerTotal && sellerProductsSuppliedCount[sellerName] > bestSingleSellerProductsCount)) {

                bestSingleSellerTotal = sellerTotals[sellerName];
                bestSingleSellerName = sellerName;
                bestSingleSellerProductsCount = sellerProductsSuppliedCount[sellerName];
            }
        }
    });

    // --- Seller Dominance Analysis ---
    let mostDominantSeller = 'N/A';
    let maxProductsSupplied = 0;
    allSellers.forEach(sellerName => {
        if (sellerProductsSuppliedCount[sellerName] > maxProductsSupplied) {
            maxProductsSupplied = sellerProductsSuppliedCount[sellerName];
            mostDominantSeller = sellerName;
        }
    });

    console.log('\n--- Seller Dominance Analysis ---');
    console.log(`Seller with the most products supplied among the selected ${top50Products.length}: ${mostDominantSeller} (${maxProductsSupplied} products)`);

    // Calculate cost if buying all products from the most dominant seller (only for products they supply)
    let totalPriceFromMostDominantSeller = 0;
    if (mostDominantSeller !== 'N/A') {
        totalPriceFromMostDominantSeller = sellerTotals[mostDominantSeller];
        console.log(`Total cost if buying from ${mostDominantSeller} only (for products they supply): $${totalPriceFromMostDominantSeller.toFixed(2)}`);
    }

    // Special Request: Calculate total cost if buying from Henry only (for products Henry supplies)
    if (sellerProductsSuppliedCount['henry'] > 0) {
        const totalPriceFromHenryOnly = sellerTotals['henry'];
        console.log(`Total cost if buying from Henry only (for products Henry supplies): $${totalPriceFromHenryOnly.toFixed(2)}`);
        console.log(`  (Henry supplied ${sellerProductsSuppliedCount['henry']} out of ${top50Products.length} products)`);
    } else {
        console.log('Henry does not supply any products in the selected list of products.');
    }


    console.log('\n--- Final Price Comparison Results ---');
    console.log(`Total price if bought using the cheapest option for each product: $${cheapestOptionTotalPrice.toFixed(2)}`);

    if (bestSingleSellerName !== 'N/A') {
        console.log(`Total price if bought from the single cheapest seller (${bestSingleSellerName}) for products they supply: $${bestSingleSellerTotal.toFixed(2)}`);
        console.log(`  (This total is for ${bestSingleSellerProductsCount} out of ${top50Products.length} products)`);
        const potentialSavings = bestSingleSellerTotal - cheapestOptionTotalPrice;
        console.log(`Potential savings by buying from cheapest option vs. best single seller: $${potentialSavings.toFixed(2)}`);
    } else {
        console.log('No single seller could supply any of the top products for a consolidated purchase.');
        console.log(`Potential savings by buying from cheapest option: $${cheapestOptionTotalPrice.toFixed(2)} (compared to no single seller option)`);
    }

    console.log('----------------------------------------\n');

    // --- New Calculation: Products with at least one price very far from others (80% difference) ---
    let productsWithExtremePriceDifference = 0;
    unifiedUpdatedData.forEach(obj => {
        const prices = [];
        for (const key in obj) {
            if (key.endsWith('_price') || key.match(/_price\d+$/)) {
                const priceValue = parsePrice(obj[key]);
                if (!isNaN(priceValue)) {
                    prices.push(priceValue);
                }
            }
        }

        // Only consider if there are at least two prices to compare
        if (prices.length >= 2) {
            const minPrice = Math.min(...prices);
            // Ensure minPrice is not zero to avoid division by zero or misleading percentage
            if (minPrice > 0) {
                // Check if any price is 80% or more higher than the minimum price
                const hasExtremePrice = prices.some(price => price > minPrice * 1.40);
                if (hasExtremePrice) {
                    productsWithExtremePriceDifference++;
                }
            }
        }
    });

    console.log('\n--- Extreme Price Difference Analysis ---');
    console.log(`Number of products with at least one price value 80% or more higher than the minimum price: ${productsWithExtremePriceDifference}`);
    console.log('-----------------------------------------\n');
}

// Run the analysis
performFinalAnalysis();
