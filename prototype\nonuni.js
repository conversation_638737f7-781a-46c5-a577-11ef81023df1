// nonUniqueMpnChecker.js

const fs = require('fs');
const path = require('path');

const unifiedFilePath = 'unified.json';
const outputFileName = 'non_unique.json';

/**
 * Analyzes unified.json to find MPN entries where a single seller
 * contributed more than one product (i.e., has a _sellerProductCounts entry > 1).
 * Logs the count and saves these objects to a new JSON file.
 */
function findNonUniqueMpnEntries() {
    let unifiedData;

    console.log(`\n--- Starting analysis of ${unifiedFilePath} ---`);

    try {
        const fileContent = fs.readFileSync(unifiedFilePath, 'utf8');
        unifiedData = JSON.parse(fileContent);
    } catch (error) {
        console.error(`Error reading or parsing ${unifiedFilePath}:`, error.message);
        console.log('Please ensure "unified.json" exists in the same directory and is valid JSON.');
        return;
    }

    if (!Array.isArray(unifiedData)) {
        console.error(`Error: The root of ${unifiedFilePath} must be an array of objects.`);
        return;
    }

    const objectsWithMultipleMpnFromSameSeller = [];
    let count = 0;

    unifiedData.forEach(obj => {
        // Ensure _sellerProductCounts exists and is an object
        if (obj._sellerProductCounts && typeof obj._sellerProductCounts === 'object') {
            let hasMultipleFromOneSeller = false;
            // Iterate through the seller product counts for the current MPN
            for (const sellerName in obj._sellerProductCounts) {
                if (obj._sellerProductCounts[sellerName] > 1) {
                    hasMultipleFromOneSeller = true;
                    // We found at least one seller with more than 1 product for this MPN
                    // No need to check other sellers for this object, move to the next object
                    break;
                }
            }

            if (hasMultipleFromOneSeller) {
                objectsWithMultipleMpnFromSameSeller.push(obj);
                count++;
            }
        }
    });

    // Save the identified objects to a new JSON file
    try {
        fs.writeFileSync(outputFileName, JSON.stringify(objectsWithMultipleMpnFromSameSeller, null, 2), 'utf8');
        console.log(`\nSuccessfully saved ${count} objects to ${outputFileName}.`);
    } catch (error) {
        console.error(`Error writing to ${outputFileName}:`, error.message);
    }

    console.log(`\n--- Analysis Complete ---`);
    console.log(`Total number of MPN entries/objects in ${unifiedFilePath} that have multiple MPN values from the same seller: ${count}`);
    console.log('---------------------------\n');
}

// Run the analysis
findNonUniqueMpnEntries();
