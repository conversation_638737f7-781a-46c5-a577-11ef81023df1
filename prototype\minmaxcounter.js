// minmaxCounter.js

const fs = require('fs');
const path = require('path');

const unifiedUpdatedFilePath = 'unified_updated.json';

/**
 * Reads unified_updated.json and counts the number of products
 * that have a 'minmax' value greater than 100.
 */
function countMinMaxGreaterThan100() {
    let unifiedUpdatedData;

    console.log(`\n--- Starting analysis of ${unifiedUpdatedFilePath} ---`);

    try {
        const fileContent = fs.readFileSync(unifiedUpdatedFilePath, 'utf8');
        unifiedUpdatedData = JSON.parse(fileContent);
    } catch (error) {
        console.error(`Error reading or parsing ${unifiedUpdatedFilePath}:`, error.message);
        console.log('Please ensure "unified_updated.json" exists in the same directory and is valid JSON.');
        return;
    }

    if (!Array.isArray(unifiedUpdatedData)) {
        console.error(`Error: The root of ${unifiedUpdatedFilePath} must be an array of objects.`);
        return;
    }

    let count = 0;
    const productsWithHighMinMax = [];

    unifiedUpdatedData.forEach(obj => {
        // Check if 'minmax' property exists and is a number
        if (typeof obj.minmax === 'number' && !isNaN(obj.minmax)) {
            if (obj.minmax > 100) {
                count++;
                productsWithHighMinMax.push(obj); // Optionally store these objects if needed later
            }
        }
    });

    console.log(`\n--- Analysis Complete ---`);
    console.log(`Total products in ${unifiedUpdatedFilePath}: ${unifiedUpdatedData.length}`);
    console.log(`Number of products with 'minmax' value greater than 100: ${count}`);
    console.log('--------------------------------------------\n');
}

// Run the analysis
countMinMaxGreaterThan100();
