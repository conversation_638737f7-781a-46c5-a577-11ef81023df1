// Import the 'fs' module for file system operations
const fs = require('fs');
const path = require('path');

// Define the path to the frontier.json file
const inputFilePath = path.join(__dirname, 'frontier.json');
// Define the path for the output file
const outputFilePath = path.join(__dirname, 'frnullunik.json');

/**
 * Processes the frontier.json file to find unique brand, subcat, and maincat
 * values for products with null MPN, and saves the counts to frnullunik.json.
 */
async function processFrontierJson() {
    let products = [];
    try {
        // Read the frontier.json file
        const data = await fs.promises.readFile(inputFilePath, 'utf8');
        products = JSON.parse(data);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.error(`Error: The file '${inputFilePath}' was not found. Please ensure it exists in the same directory as the script.`);
        } else {
            console.error('Error reading or parsing frontier.json:', error);
        }
        return; // Exit if file not found or parsing fails
    }

    const totalProducts = products.length;
    console.log(`Total products read from frontier.json: ${totalProducts}`);

    // Filter products where mpn is null
    const productsWithNullMpn = products.filter(product => product.mpn === null);
    const countNullMpn = productsWithNullMpn.length;
    console.log(`Number of products with null MPN: ${countNullMpn}`);

    // Initialize objects to store unique values and their counts
    const uniqueBrands = {};
    const uniqueSubcats = {};
    const uniqueMaincats = {};

    // Iterate through products with null MPN to collect unique values and counts
    productsWithNullMpn.forEach(product => {
        // Process 'brand'
        if (product.brand) {
            const brand = product.brand.trim();
            uniqueBrands[brand] = (uniqueBrands[brand] || 0) + 1;
        }

        // Process 'subcat'
        if (product.subcat) {
            const subcat = product.subcat.trim();
            uniqueSubcats[subcat] = (uniqueSubcats[subcat] || 0) + 1;
        }

        // Process 'maincat'
        if (product.maincat) {
            const maincat = product.maincat.trim();
            uniqueMaincats[maincat] = (uniqueMaincats[maincat] || 0) + 1;
        }
    });

    // Prepare the output data structure
    const outputData = {
        summary: {
            totalProductsInInput: totalProducts,
            productsWithNullMpn: countNullMpn
        },
        uniqueValues: {
            brands: uniqueBrands,
            subcategories: uniqueSubcats,
            maincategories: uniqueMaincats
        }
    };

    try {
        // Save the processed data to frnullunik.json
        await fs.promises.writeFile(outputFilePath, JSON.stringify(outputData, null, 2), 'utf8');
        console.log(`Successfully saved unique values and counts to: ${outputFilePath}`);
    } catch (error) {
        console.error('Error writing frnullunik.json:', error);
    }
}

// Execute the processing function
processFrontierJson();
