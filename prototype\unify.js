// Import the 'fs' module for file system operations
const fs = require('fs');
const path = require('path');

// Define the list of JSON files to process
const fileNames = [
    'benco.json',
    'henry.json',
    'dds.json',
    'optimus.json',
    'frontier.json'
];

// Define the path for the output summary file
const outputFilePath = path.join(__dirname, 'non_unique_mpn_summary.json');

/**
 * Processes a single JSON file to find objects with non-unique MPNs
 * and then extracts unique brand and maincat values from them.
 * @param {string} fileName - The name of the JSON file to process.
 * @returns {Promise<object>} A promise that resolves to an object containing
 * the summary for the processed file.
 */
async function processSingleFile(fileName) {
    const filePath = path.join(__dirname, fileName);
    let products = [];

    try {
        const data = await fs.promises.readFile(filePath, 'utf8');
        products = JSON.parse(data);
        console.log(`Successfully loaded ${fileName}. Total products: ${products.length}`);
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.error(`Error: The file '${fileName}' was not found at '${filePath}'. Skipping this file.`);
        } else {
            console.error(`Error reading or parsing ${fileName}:`, error);
        }
        return {
            fileName: fileName,
            status: 'Failed to load/parse',
            error: error.message
        };
    }

    // Step 1: Count MPN occurrences to identify non-unique ones
    const mpnCounts = new Map();
    products.forEach(product => {
        // Ensure mpn is treated consistently (e.g., convert to string, handle null/undefined)
        const mpn = product.mpn !== null && product.mpn !== undefined ? String(product.mpn) : 'NULL_MPN';
        mpnCounts.set(mpn, (mpnCounts.get(mpn) || 0) + 1);
    });

    // Step 2: Filter products that have non-unique MPN values
    const productsWithNonUniqueMpn = products.filter(product => {
        const mpn = product.mpn !== null && product.mpn !== undefined ? String(product.mpn) : 'NULL_MPN';
        return mpnCounts.get(mpn) > 1;
    });

    console.log(`  ${fileName}: Found ${productsWithNonUniqueMpn.length} products with non-unique MPNs.`);

    // Step 3: Find unique brand and maincat values from these filtered products
    const uniqueBrands = new Set();
    const uniqueMaincats = new Set();

    productsWithNonUniqueMpn.forEach(product => {
        if (product.brand && typeof product.brand === 'string') {
            uniqueBrands.add(product.brand.trim());
        }
        if (product.maincat && typeof product.maincat === 'string') {
            uniqueMaincats.add(product.maincat.trim());
        }
    });

    return {
        fileName: fileName,
        totalProducts: products.length,
        productsWithNonUniqueMpnCount: productsWithNonUniqueMpn.length,
        uniqueBrandsForNonUniqueMpn: Array.from(uniqueBrands).sort(),
        uniqueMaincatsForNonUniqueMpn: Array.from(uniqueMaincats).sort()
    };
}

/**
 * Main function to process all specified JSON files and generate a summary.
 */
async function processAllFiles() {
    const allFilesSummary = [];

    for (const fileName of fileNames) {
        const summary = await processSingleFile(fileName);
        allFilesSummary.push(summary);
    }

    try {
        // Save the comprehensive summary to the output file
        await fs.promises.writeFile(outputFilePath, JSON.stringify(allFilesSummary, null, 2), 'utf8');
        console.log(`\nAll files processed. Summary saved to: ${outputFilePath}`);
    } catch (error) {
        console.error('Error writing the summary file:', error);
    }
}

// Execute the main processing function
processAllFiles();
